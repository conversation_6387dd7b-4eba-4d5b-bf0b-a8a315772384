import {injectable, BindingScope, inject} from '@loopback/core';
import {Metric, OrganizationPlan, OrganizationPlanRelations, PlanFeature, PlanFeatureWithRelations} from '../models';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {FeatureSettingRepository, OrganizationRepository, OrganizationPlanRepository, PlanFeatureRepository, PlanRepository, LoyaltyProgramRepository, PromotionalCampaignRepository, OrganizationMetricRepository, MetricRepository} from '../repositories';
import {PromotionalCampaignController} from '../controllers';

// const featureStateCache = new Map<number, any>();

@injectable({scope: BindingScope.TRANSIENT})
export class FeatureService {
	constructor(
		@repository(FeatureSettingRepository)
		public featureSettingRepository: FeatureSettingRepository,
		@repository(OrganizationRepository)
		public organizationRepository: OrganizationRepository,
		@repository(OrganizationPlanRepository)
		public organizationPlanRepository: OrganizationPlanRepository,
		@repository(OrganizationMetricRepository)
		public organizationMetricRepository: OrganizationMetricRepository,
		@repository(PlanFeatureRepository)
		public planFeatureRepository: PlanFeatureRepository,
		@repository(PlanRepository)
		public planRepository: PlanRepository,
		@repository(LoyaltyProgramRepository)
		public loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(PromotionalCampaignRepository)
		public promotionalCampaignRepository: PromotionalCampaignRepository,
		@inject('controllers.PromotionalCampaignController')
		private promotionalCampaignController: PromotionalCampaignController,
		@repository(MetricRepository)
		public metricRepository: MetricRepository,
	) { }

	async getFeatureStates(
		orgId: number
	): Promise<{ /*organization: any; */ plan: any; features: (PlanFeatureWithRelations & {available: boolean, enabled: boolean, live: boolean})[]}> {
		// if (featureStateCache.has(orgId)) {
		// 	return featureStateCache.get(orgId);
		// }


		// // Fetch the organization details
		const org = await this.organizationRepository.findById(orgId);
		// const startDate = new Date(org!.createdDate!);
		// const trialLengthDays = 14;

		// const date = new Date();
		// const endDate = new Date(startDate);
		// endDate.setDate(startDate.getDate() + trialLengthDays);

		// Fetch the current plan of the organization
		const organizationPlan = await this.organizationPlanRepository.findOne({
			where: {orgId, status: 'ACTIVE'},
			include: [{relation: 'plan'}],
		}) as OrganizationPlan & OrganizationPlanRelations;
		const plan = (organizationPlan
			? organizationPlan.plan
			: undefined);

		if (!plan) {
			// throw new HttpErrors.NotFound('Organization plan not found');
			console.warn('Organization plan not found');

			return {
				// organization,
				plan: undefined,
				features: [],
			};
		}

		// Fetch the plan features
		const planFeatures = await this.planFeatureRepository.find({
			where: {planId: plan.id},
			include: [{relation: 'feature'}],
		});

		// find any feature settings for the organization, related to the plan features' features
		const featureSettings = await this.featureSettingRepository.find({
			where: {organizationId: orgId},
		});

		// merge the plan features and feature settings
		const features = planFeatures.map(planFeature => {
			const featureSetting = featureSettings.find(setting => setting.name === planFeature.featureId);
			return {
				...planFeature,
				available: planFeature?.enabled,
				enabled: planFeature?.enabled && featureSetting?.enabled,
				live: featureSetting?.live,
			};
		}) as (PlanFeatureWithRelations & {available: boolean, enabled: boolean, live: boolean})[];

		// featureStateCache.set(orgId, {
		// 	// organization,
		// 	// plan,
		// 	features,
		// });

		return {
			// organization,
			plan,
			features,
		};
	}

	async isFeatureAvailable(featureId: string, orgId: number): Promise<boolean> {
		const allFeatures = await this.getFeatureStates(orgId);
		const feature = allFeatures.features.find(f => f.featureId === featureId);
		return feature?.available ?? false;
	}

	async isFeatureEnabled(featureId: string, orgId: number): Promise<boolean> {
		const allFeatures = await this.getFeatureStates(orgId);
		const feature = allFeatures.features.find(f => f.featureId === featureId);
		return (feature?.available && feature?.enabled) ?? false;
	}

	async isFeatureLive(featureId: string, orgId: number): Promise<boolean> {
		const allFeatures = await this.getFeatureStates(orgId);
		const feature = allFeatures.features.find(f => f.featureId === featureId);
		return (feature?.available && feature?.live) ?? false;
	}

	async isFeatureLiveAndEnabled(featureId: string, orgId: number): Promise<boolean> {
		const allFeatures = await this.getFeatureStates(orgId);
		const feature = allFeatures.features.find(f => f.featureId === featureId);
		return (feature?.available && feature?.enabled && feature?.live) ?? false;
	}

	// async invalidateCache(orgId: number) {
	// 	featureStateCache.delete(orgId);
// 	featureStateCache.delete(orgId.toString() as any);
	// 	featureStateCache.delete(Number(orgId));
	// }

	async updateLoyaltyAndGwpEnabled(orgId: number) {
		await this.updateLoyaltyEnabled(orgId);
		await this.updateGwpEnabled(orgId);
		await this.updateMetrics(orgId);
	}

	async updateLoyaltyEnabled(orgId: number) {
		const enabled = await this.isFeatureAvailable('loyalty-app', orgId);
		console.log(`Setting loyalty to ${enabled} for org ${orgId}`);

		await this.setLoyaltyEnabled(orgId, enabled);
	}

	async updateGwpEnabled(orgId: number) {
		const enabled = await this.isFeatureAvailable('gwp-features', orgId);
		console.log(`Setting GWP to ${enabled} for org ${orgId}`);

		await this.setGwpEnabled(orgId, enabled);
	}

	async updateMetrics(orgId: number) {
		// Fetch the current plan for the organization
		const organizationPlan = (await this.organizationPlanRepository.findOne({
			where: {orgId, status: 'ACTIVE'},
			include: [{relation: 'plan'}],
		})) as OrganizationPlan & OrganizationPlanRelations;

		const plan = organizationPlan?.plan;

		if (!plan) {
			console.warn(
				`No active plan found for organization ID: ${orgId}. Deleting all metrics for this organization.`,
			);
			// Delete all metrics if no plan exists
			await this.organizationMetricRepository.deleteAll({orgId});
			return;
		}

		// Fetch plan features and their associated features for the current plan
		const planFeatures = await this.planFeatureRepository.find({
			where: {planId: plan.id,enabled: true},
			include: [{relation: 'feature'}], // Include the related Feature
		});

		// Get the list of featureIds from planFeatures
		const featureIds = planFeatures.map(pf => pf.featureId);

		if (featureIds.length === 0) {
			// If there are no features, delete all OrganizationMetrics
			await this.organizationMetricRepository.deleteAll({orgId});
			return;
		}

		// Fetch all Metrics associated with these features
		const metrics = await this.metricRepository.find({
			where: {featureId: {inq: featureIds},isDefault: true,},
		});

		const metricIds = metrics.map(m => m.id!);

		// Fetch existing OrganizationMetrics for this org and these metricIds
		const existingOrgMetrics = await this.organizationMetricRepository.find({
			where: {
				orgId,
				metricId: {inq: metricIds},
			},
		});

		// Create a Set of existing metricIds
		const existingMetricIds = new Set(existingOrgMetrics.map(om => om.metricId));

		// Find metrics that need to be created
		const metricsToCreate = metrics.filter(metric => !existingMetricIds.has(metric.id!));

		// Prepare new OrganizationMetrics to create
		const newOrgMetrics = metricsToCreate.map(metric => ({
			orgId,
			metricId: metric.id!,
			runFrequency: metric.defaultRunFrequency,
			// Add other properties as needed
		}));

		// Create new OrganizationMetrics in bulk
		if (newOrgMetrics.length > 0) {
			await this.organizationMetricRepository.createAll(newOrgMetrics);
		}

		// Delete OrganizationMetrics that are no longer associated with the plan
		await this.organizationMetricRepository.deleteAll({
			orgId,
			metricId: {nin: metricIds},
		});
	}

	private async setLoyaltyEnabled(orgId: number, active: boolean) {
		if (active) {
			return;
		}

		const program = await this.loyaltyProgramRepository.findOne({
			where: {orgId},
		});

		if (program) {
			await this.loyaltyProgramRepository.updateById(program.id, {active});
		}
	}

	private async setGwpEnabled(orgId: number, active: boolean) {
		if (active) {
			return;
		}


		const campaigns = await this.promotionalCampaignRepository.find({
			where: {orgId},
		});

		console.log('Campaigns', campaigns);

		for (const campaign of campaigns) {
			// await this.promotionalCampaignRepository.updateById(campaign.id, { active });
			try {
				console.log('Deleting free gift for campaign', campaign.id);
				await this.promotionalCampaignController.deleteFreeGift({id: campaign.id}, campaign.id!, orgId);
			} catch (e) {
				console.error('Error deleting free gift for campaign', campaign.id, e);
			}
		}
	}
}
